import { test, expect } from "@playwright/test"

// Shopping Cart Functionality: Add a product to the cart and verify cart icon updates
test("Add a product to the cart and verify cart icon updates", async ({ page }) => {
  // Given the user is on the Login Page
  await page.goto("https://www.saucedemo.com/")

  // When the user logs in with username "standard_user" and password "secret_sauce"
  await page.fill('input[data-test="username"]', "standard_user")
  await page.fill('input[data-test="password"]', "secret_sauce")
  await page.click('input[data-test="login-button"]')

  // And the user navigates to the Inventory Page (should be redirected after login)
  await expect(page).toHaveURL(/inventory/)

  // And the cart icon is initially empty
  const cartBadge = page.locator(".shopping_cart_badge")
  await expect(cartBadge).toHaveCount(0)

  // When the user clicks the "Add to cart" button for a product
  await page.click('button[data-test^="add-to-cart-"]')

  // Then the cart icon should update to reflect "1" item
  await expect(cartBadge).toHaveText("1")
})
